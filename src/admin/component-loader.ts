import { ComponentLoader } from 'adminjs';

const componentLoader = new ComponentLoader();


const Components = {
    Dashboard: componentLoader.add('Dashboard', './dashboard'),
    BufferDisplay: componentLoader.add('BufferDisplay', './components/BufferDisplay'),
    DateDisplay: componentLoader.add('DateDisplay', './components/DateDisplay'),
    UserReference: componentLoader.add('UserReference', './components/UserReference'),
    ServiceReference: componentLoader.add('ServiceReference', './components/ServiceReference'),
    DatePaidDisplay: componentLoader.add('DatePaidDisplay', './components/DatePaidDisplay'),
    UserServices: componentLoader.add('UserServices', './components/UserServices'),
    UserInvoices: componentLoader.add('UserInvoices', './components/UserInvoices'),
    UserDetails: componentLoader.add('UserDetails', './components/UserDetails'),
    InvoiceItems: componentLoader.add('InvoiceItems', './components/InvoiceItems'),
    ServiceInvoices: componentLoader.add('ServiceInvoices', './components/ServiceInvoices'),
    SuperAPI: componentLoader.add('SuperAPI', './components/SuperAPI'),
    EditSuperAPI: componentLoader.add('EditSuperAPI', './components/EditSuperAPI'),
    MyDemoAction: componentLoader.add('MyDemoAction', './components/MyDemoAction'),
    // other custom components
 }
export {componentLoader, Components };
