import React, { useEffect, useState } from 'react';
import { Box, Text, Loader } from '@adminjs/design-system';
import { ApiClient, ActionProps } from 'adminjs';
import styled from 'styled-components';
import { SuperapiProductService } from '../resources/superapi-types.js';

const InfoCard = styled(Box)`
  background: #f5f7f9;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
`;

interface SuperAPIProps {
  serviceId: string;
}

// 支持两种使用方式：作为独立组件或作为 AdminJS action 组件
type SuperAPIComponentProps = SuperAPIProps | ActionProps;

const formatBytes = (bytes: number) => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const value = bytes / Math.pow(k, i);
  
  return `${value.toFixed(2)} ${sizes[i]}`;
};

const SuperAPI: React.FC<SuperAPIComponentProps> = (props) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [apiInfo, setApiInfo] = useState<SuperapiProductService | null>(null);
  const api = new ApiClient();

  // 检查是否是 ActionProps（来自 AdminJS action）
  const isActionProps = 'record' in props;
  const serviceId = isActionProps ? props.record?.params?.id : (props as SuperAPIProps).serviceId;

  useEffect(() => {
    console.log('=== SuperAPI Component Debug ===');
    console.log('SuperAPI component props:', props);
    console.log('SuperAPI component props keys:', Object.keys(props));
    console.log('isActionProps:', isActionProps);
    console.log('serviceId:', serviceId);

    if (isActionProps) {
      // 从 AdminJS action 的 record 中获取 SuperAPI 数据
      const record = (props as ActionProps).record;
      console.log('record:', record);
      console.log('record keys:', record ? Object.keys(record) : 'no record');
      console.log('record.params:', record?.params);
      console.log('record.params keys:', record?.params ? Object.keys(record.params) : 'no params');

      // 尝试从不同位置获取 superapiData
      const superapiData = record?.params?.superapiData;
      console.log('superapiData from record.params.superapiData:', superapiData);

      // 也检查 record 的其他可能位置
      console.log('Full record structure:', JSON.stringify(record, null, 2));

      if (superapiData) {
        console.log('Found superapiData:', superapiData);
        setApiInfo(superapiData as SuperapiProductService);
        setError(null);
      } else {
        console.log('No superapiData found in record.params');
        console.log('Available params keys:', record?.params ? Object.keys(record.params) : 'no params');
        setError('No SuperAPI data found in record. Check console for debug info.');
      }
    } else {
      // 独立组件使用，需要调用 API
      console.log('Independent component usage detected');
      setError('Independent component usage not implemented yet');
    }

    setLoading(false);
    console.log('=== End SuperAPI Component Debug ===');
  }, [props, serviceId, isActionProps]);

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <Box>
        <Text color="error">{error}</Text>
      </Box>
    );
  }

  if (!apiInfo) {
    return (
      <Box>
        <Text>No SuperAPI information available</Text>
      </Box>
    );
  }

  return (
    <InfoCard>
      <Box>
        <Text variant="h5">Service Details</Text>
        <Box mt="lg">
          <Text>ID: {apiInfo.id}</Text>
          <Text>Client ID: {apiInfo.clientId}</Text>
          <Text>Client: {apiInfo.client}</Text>
          <Text>Username: {apiInfo.username}</Text>
          <Text>Password: {apiInfo.password}</Text>
          <Text>Status: {apiInfo.status}</Text>
          <Text>Quota: {apiInfo.quota}</Text>
          <Text>Type: {apiInfo.type}</Text>
          <Text>Upload: {formatBytes(apiInfo.upload)}</Text>
          <Text>Download: {formatBytes(apiInfo.download)}</Text>
          <Text>Reset Date: {apiInfo.resetDate}</Text>
          <Text>Updated At: {apiInfo.updatedAt}</Text>
          <Text>Expired At: {apiInfo.expiredAt}</Text>
          <Text>Speed Limit: {apiInfo.speedLimit}</Text>
        </Box>
      </Box>
    </InfoCard>
  );
};

export default SuperAPI; 