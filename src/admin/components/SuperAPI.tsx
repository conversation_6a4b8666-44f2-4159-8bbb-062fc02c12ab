import React, { useEffect, useState } from 'react';
import {
  Box,
  Text,
  Loader,
  Badge,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Button,
  Icon
} from '@adminjs/design-system';

const InfoCard = ({ children, title }) => (
  <Box
    style={{
      background: 'white',
      border: '1px solid #e9ecef',
      borderRadius: '8px',
      padding: '0',
      marginBottom: '16px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
    }}
  >
    {title && (
      <Box
        style={{
          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
          padding: '16px 20px',
          borderBottom: '1px solid #e9ecef',
          borderRadius: '8px 8px 0 0'
        }}
      >
        <Text variant="h6" style={{ margin: 0, fontWeight: 600, color: '#495057' }}>
          {title}
        </Text>
      </Box>
    )}
    <Box style={{ padding: '20px' }}>
      {children}
    </Box>
  </Box>
);

const formatBytes = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const value = bytes / Math.pow(k, i);

  return `${value.toFixed(2)} ${sizes[i]}`;
};

const formatDate = (dateString) => {
  if (!dateString || dateString.includes('1969') || dateString.includes('1899')) return 'Not set';
  try {
    return new Date(dateString).toLocaleString();
  } catch {
    return 'Invalid date';
  }
};

const getStatusBadge = (status) => {
  if (status === 1) {
    return <Badge variant="success">Active</Badge>;
  } else {
    return <Badge variant="danger">Suspended</Badge>;
  }
};

const getResetTypeBadge = (type) => {
  if (type === 1) {
    return <Badge variant="primary">Monthly</Badge>;
  } else {
    return <Badge variant="secondary">Never</Badge>;
  }
};

const SuperAPI = (props) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [apiInfo, setApiInfo] = useState(null);

  // 检查是否是 ActionProps（来自 AdminJS action）
  const isActionProps = 'record' in props;
  const serviceId = isActionProps ? props.record?.params?.id : props.serviceId;

  useEffect(() => {
    console.log('=== SuperAPI Component Debug ===');
    console.log('SuperAPI component props:', props);
    console.log('SuperAPI component props keys:', Object.keys(props));
    console.log('isActionProps:', isActionProps);
    console.log('serviceId:', serviceId);

    if (isActionProps) {
      // 从 AdminJS action 的 record 中获取 SuperAPI 数据
      const record = props.record;
      console.log('record:', record);
      console.log('record keys:', record ? Object.keys(record) : 'no record');
      console.log('record.params:', record?.params);
      console.log('record.params keys:', record?.params ? Object.keys(record.params) : 'no params');

      // 尝试从不同位置获取 superapiData
      const superapiData = record?.params?.superapiData;
      console.log('superapiData from record.params.superapiData:', superapiData);

      // 也检查 record 的其他可能位置
      console.log('Full record structure:', JSON.stringify(record, null, 2));

      if (superapiData) {
        console.log('Found superapiData:', superapiData);
        setApiInfo(superapiData);
        setError(null);
      } else {
        console.log('No superapiData found in record.params');
        console.log('Available params keys:', record?.params ? Object.keys(record.params) : 'no params');
        setError('No SuperAPI data found in record. Check console for debug info.');
      }
    } else {
      // 独立组件使用，需要调用 API
      console.log('Independent component usage detected');
      setError('Independent component usage not implemented yet');
    }

    setLoading(false);
    console.log('=== End SuperAPI Component Debug ===');
  }, [props, serviceId, isActionProps]);

  if (loading) {
    return <Loader />;
  }

  if (error) {
    return (
      <Box>
        <Text color="error">{error}</Text>
      </Box>
    );
  }

  if (!apiInfo) {
    return (
      <Box>
        <Text>No SuperAPI information available</Text>
      </Box>
    );
  }

  return (
    <Box>
      {/* 基本信息卡片 */}
      <InfoCard title="SuperAPI Service Information">
        <Box display="grid" gridTemplateColumns="1fr 1fr" gap="lg">
          <Box>
            <Box mb="md">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
                Service ID
              </Text>
              <Text variant="lg" style={{ fontWeight: 600 }}>
                {apiInfo.id}
              </Text>
            </Box>

            <Box mb="md">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
                Client ID
              </Text>
              <Text variant="lg" style={{ fontWeight: 600 }}>
                {apiInfo.clientId}
              </Text>
            </Box>

            <Box mb="md">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
                Client
              </Text>
              <Text variant="lg" style={{ fontWeight: 600 }}>
                {apiInfo.client}
              </Text>
            </Box>

            <Box mb="md">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
                Status
              </Text>
              <Box mt="xs">
                {getStatusBadge(apiInfo.status)}
              </Box>
            </Box>
          </Box>

          <Box>
            <Box mb="md">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
                Username
              </Text>
              <Text variant="lg" style={{ fontWeight: 600, fontFamily: 'monospace' }}>
                {apiInfo.username}
              </Text>
            </Box>

            <Box mb="md">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
                Password
              </Text>
              <Text variant="lg" style={{ fontWeight: 600, fontFamily: 'monospace' }}>
                {apiInfo.password}
              </Text>
            </Box>

            <Box mb="md">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
                Reset Type
              </Text>
              <Box mt="xs">
                {getResetTypeBadge(apiInfo.type)}
              </Box>
            </Box>

            <Box mb="md">
              <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
                Speed Limit
              </Text>
              <Text variant="lg" style={{ fontWeight: 600 }}>
                {apiInfo.speedLimit ? formatBytes(apiInfo.speedLimit) + '/s' : 'Unlimited'}
              </Text>
            </Box>
          </Box>
        </Box>
      </InfoCard>

      {/* 使用量统计卡片 */}
      <InfoCard title="Usage Statistics">
        <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap="lg">
          <Box
            p="lg"
            style={{
              backgroundColor: '#f8f9fa',
              borderRadius: '6px',
              border: '1px solid #e9ecef',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Upload Usage
            </Text>
            <Text variant="h4" mt="xs" color="primary" style={{ fontWeight: 700 }}>
              {formatBytes(apiInfo.upload)}
            </Text>
          </Box>

          <Box
            p="lg"
            style={{
              backgroundColor: '#f8f9fa',
              borderRadius: '6px',
              border: '1px solid #e9ecef',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Download Usage
            </Text>
            <Text variant="h4" mt="xs" color="primary" style={{ fontWeight: 700 }}>
              {formatBytes(apiInfo.download)}
            </Text>
          </Box>

          <Box
            p="lg"
            style={{
              backgroundColor: '#fff3cd',
              borderRadius: '6px',
              border: '1px solid #ffeaa7',
              textAlign: 'center'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Quota Configuration
            </Text>
            <Text variant="sm" mt="xs" style={{ fontWeight: 600, fontFamily: 'monospace' }}>
              {apiInfo.quota}
            </Text>
          </Box>
        </Box>
      </InfoCard>

      {/* 时间信息卡片 */}
      <InfoCard title="Time Information">
        <Box display="grid" gridTemplateColumns="1fr 1fr 1fr" gap="lg">
          <Box>
            <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
              Reset Date
            </Text>
            <Text variant="lg" style={{ fontWeight: 600 }}>
              {formatDate(apiInfo.resetDate)}
            </Text>
          </Box>

          <Box>
            <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
              Updated At
            </Text>
            <Text variant="lg" style={{ fontWeight: 600 }}>
              {formatDate(apiInfo.updatedAt)}
            </Text>
          </Box>

          <Box>
            <Text variant="sm" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px', marginBottom: '4px' }}>
              Expired At
            </Text>
            <Text variant="lg" style={{ fontWeight: 600, color: new Date(apiInfo.expiredAt) < new Date() ? '#dc3545' : '#28a745' }}>
              {formatDate(apiInfo.expiredAt)}
            </Text>
          </Box>
        </Box>
      </InfoCard>
    </Box>
  );
};

export default SuperAPI; 