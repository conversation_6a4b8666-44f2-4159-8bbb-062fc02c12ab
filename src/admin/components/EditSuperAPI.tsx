import React, { useEffect, useState } from 'react';
import {
  Box,
  Text,
  Loader,
  Button,
  Input,
  FormGroup,
  Label,
  MessageBox
} from '@adminjs/design-system';
import { ApiClient } from 'adminjs';

const EditSuperAPI = (props) => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [apiInfo, setApiInfo] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    quota: '',
    status: 1,
    expiredAt: '',
    speedLimit: 0,
    resetType: 0
  });
  
  const api = new ApiClient();
  const serviceId = props.record?.params?.id;

  useEffect(() => {
    console.log('=== EditSuperAPI Component Debug ===');
    console.log('EditSuperAPI component props:', props);
    console.log('serviceId:', serviceId);

    if (props.record?.params?.superapiData) {
      const superapiData = props.record.params.superapiData;
      console.log('Found superapiData:', superapiData);
      setApiInfo(superapiData);
      
      // 初始化表单数据
      setFormData({
        username: superapiData.username || '',
        password: superapiData.password || '',
        quota: superapiData.quota || '',
        status: superapiData.status || 1,
        expiredAt: superapiData.expiredAt ? new Date(superapiData.expiredAt).toISOString().split('T')[0] : '',
        speedLimit: superapiData.speedLimit || 0,
        resetType: superapiData.type || 0
      });
      setError(null);
    } else {
      setError('No SuperAPI data found in record.');
    }

    setLoading(false);
    console.log('=== End EditSuperAPI Component Debug ===');
  }, [props, serviceId]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // 调用 AdminJS API 来更新 SuperAPI 数据
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: serviceId,
        actionName: 'updateSuperapi',
        data: formData
      });

      if (response.data?.success) {
        setSuccess('SuperAPI information updated successfully!');
        // 可以选择刷新数据或关闭弹窗
      } else {
        setError('Failed to update SuperAPI information.');
      }
    } catch (err) {
      console.error('Error updating SuperAPI:', err);
      setError('Network error occurred while updating SuperAPI information.');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: serviceId,
        actionName: 'resetSuperapi'
      });

      if (response.data?.success) {
        setSuccess('SuperAPI service reset successfully!');
      } else {
        setError('Failed to reset SuperAPI service.');
      }
    } catch (err) {
      console.error('Error resetting SuperAPI:', err);
      setError('Network error occurred while resetting SuperAPI service.');
    } finally {
      setSaving(false);
    }
  };

  const handleRedeem = async () => {
    const delta = prompt('Enter the amount to redeem (in bytes):');
    if (!delta || isNaN(Number(delta))) {
      setError('Please enter a valid number for redeem amount.');
      return;
    }

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: serviceId,
        actionName: 'redeemSuperapi',
        data: { delta: parseInt(delta) }
      });

      if (response.data?.success) {
        setSuccess(`Successfully redeemed ${delta} bytes from SuperAPI service!`);
      } else {
        setError('Failed to redeem from SuperAPI service.');
      }
    } catch (err) {
      console.error('Error redeeming SuperAPI:', err);
      setError('Network error occurred while redeeming from SuperAPI service.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (error && !apiInfo) {
    return (
      <Box>
        <Text color="error">{error}</Text>
      </Box>
    );
  }

  return (
    <Box p="xl">
      <Text variant="h4" mb="xl">Edit SuperAPI Information</Text>
      
      {error && (
        <MessageBox mb="lg" variant="danger">
          {error}
        </MessageBox>
      )}
      
      {success && (
        <MessageBox mb="lg" variant="success">
          {success}
        </MessageBox>
      )}

      <Box mb="xl">
        <Text variant="h6" mb="lg">Service Information</Text>
        <Box display="grid" gridTemplateColumns="1fr 1fr" gap="lg">
          <Box>
            <Text><strong>Service ID:</strong> {apiInfo?.id}</Text>
            <Text><strong>Client ID:</strong> {apiInfo?.clientId}</Text>
            <Text><strong>Client:</strong> {apiInfo?.client}</Text>
          </Box>
          <Box>
            <Text><strong>Upload:</strong> {apiInfo?.upload ? `${(apiInfo.upload / 1024 / 1024 / 1024).toFixed(2)} GB` : 'N/A'}</Text>
            <Text><strong>Download:</strong> {apiInfo?.download ? `${(apiInfo.download / 1024 / 1024 / 1024).toFixed(2)} GB` : 'N/A'}</Text>
            <Text><strong>Reset Date:</strong> {apiInfo?.resetDate ? new Date(apiInfo.resetDate).toLocaleDateString() : 'N/A'}</Text>
          </Box>
        </Box>
      </Box>

      <Box mb="xl">
        <Text variant="h6" mb="lg">Editable Fields</Text>
        <Box display="grid" gridTemplateColumns="1fr 1fr" gap="lg">
          <FormGroup>
            <Label>Username</Label>
            <Input
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              disabled={saving}
            />
          </FormGroup>

          <FormGroup>
            <Label>Password</Label>
            <Input
              type="password"
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              disabled={saving}
            />
          </FormGroup>

          <FormGroup>
            <Label>Quota</Label>
            <Input
              value={formData.quota}
              onChange={(e) => handleInputChange('quota', e.target.value)}
              disabled={saving}
              placeholder="e.g., [{'traffic': 107374182400, 'speed': 2621440}]"
            />
          </FormGroup>

          <FormGroup>
            <Label>Status</Label>
            <Box>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', parseInt(e.target.value))}
                disabled={saving}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #ccc',
                  borderRadius: '4px'
                }}
              >
                <option value={0}>Suspended</option>
                <option value={1}>Active</option>
              </select>
            </Box>
          </FormGroup>

          <FormGroup>
            <Label>Expired At</Label>
            <Input
              type="date"
              value={formData.expiredAt}
              onChange={(e) => handleInputChange('expiredAt', e.target.value)}
              disabled={saving}
            />
          </FormGroup>

          <FormGroup>
            <Label>Speed Limit (bytes/s)</Label>
            <Input
              type="number"
              value={formData.speedLimit}
              onChange={(e) => handleInputChange('speedLimit', parseInt(e.target.value) || 0)}
              disabled={saving}
            />
          </FormGroup>

          <FormGroup>
            <Label>Reset Type</Label>
            <Box>
              <select
                value={formData.resetType}
                onChange={(e) => handleInputChange('resetType', parseInt(e.target.value))}
                disabled={saving}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #ccc',
                  borderRadius: '4px'
                }}
              >
                <option value={0}>Never</option>
                <option value={1}>Monthly</option>
              </select>
            </Box>
          </FormGroup>
        </Box>
      </Box>

      <Box display="flex" gap="md" justifyContent="flex-start">
        <Button
          variant="primary"
          onClick={handleSave}
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
        
        <Button
          variant="danger"
          onClick={handleReset}
          disabled={saving}
        >
          Reset Service
        </Button>
        
        <Button
          variant="secondary"
          onClick={handleRedeem}
          disabled={saving}
        >
          Redeem Data
        </Button>
      </Box>
    </Box>
  );
};

export default EditSuperAPI;
