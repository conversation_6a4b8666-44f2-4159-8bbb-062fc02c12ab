import React, { useEffect, useState } from 'react';
import {
  Box,
  Text,
  Loader,
  Button,
  Input,
  FormGroup,
  Label,
  MessageBox,
  Badge,
  Icon
} from '@adminjs/design-system';
import { ApiClient } from 'adminjs';

const InfoCard = ({ children, title, variant = 'default' }) => {
  const getCardStyle = () => {
    const baseStyle = {
      background: 'white',
      border: '1px solid #e9ecef',
      borderRadius: '8px',
      padding: '0',
      marginBottom: '20px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
    };

    if (variant === 'primary') {
      return {
        ...baseStyle,
        border: '1px solid #007bff',
        boxShadow: '0 2px 8px rgba(0,123,255,0.15)'
      };
    }

    return baseStyle;
  };

  const getHeaderStyle = () => {
    if (variant === 'primary') {
      return {
        background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
        padding: '16px 20px',
        borderBottom: '1px solid #007bff',
        borderRadius: '8px 8px 0 0',
        color: 'white'
      };
    }

    return {
      background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
      padding: '16px 20px',
      borderBottom: '1px solid #e9ecef',
      borderRadius: '8px 8px 0 0'
    };
  };

  return (
    <Box style={getCardStyle()}>
      {title && (
        <Box style={getHeaderStyle()}>
          <Text variant="h6" style={{
            margin: 0,
            fontWeight: 600,
            color: variant === 'primary' ? 'white' : '#495057'
          }}>
            {title}
          </Text>
        </Box>
      )}
      <Box style={{ padding: '20px' }}>
        {children}
      </Box>
    </Box>
  );
};

const EditSuperAPI = (props) => {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [apiInfo, setApiInfo] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    quota: '',
    status: 1,
    expiredAt: '',
    speedLimit: 0,
    resetType: 0
  });
  
  const api = new ApiClient();
  const serviceId = props.record?.params?.id;

  useEffect(() => {
    console.log('=== EditSuperAPI Component Debug ===');
    console.log('EditSuperAPI component props:', props);
    console.log('serviceId:', serviceId);

    if (props.record?.params?.superapiData) {
      const superapiData = props.record.params.superapiData;
      console.log('Found superapiData:', superapiData);
      setApiInfo(superapiData);
      
      // 初始化表单数据
      setFormData({
        username: superapiData.username || '',
        password: superapiData.password || '',
        quota: superapiData.quota || '',
        status: superapiData.status || 1,
        expiredAt: superapiData.expiredAt ? new Date(superapiData.expiredAt).toISOString().split('T')[0] : '',
        speedLimit: superapiData.speedLimit || 0,
        resetType: superapiData.type || 0
      });
      setError(null);
    } else {
      setError('No SuperAPI data found in record.');
    }

    setLoading(false);
    console.log('=== End EditSuperAPI Component Debug ===');
  }, [props, serviceId]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      // 调用 AdminJS API 来更新 SuperAPI 数据
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: serviceId,
        actionName: 'updateSuperapi',
        data: formData
      });

      if (response.data?.success) {
        setSuccess('SuperAPI information updated successfully!');
        // 可以选择刷新数据或关闭弹窗
      } else {
        setError('Failed to update SuperAPI information.');
      }
    } catch (err) {
      console.error('Error updating SuperAPI:', err);
      setError('Network error occurred while updating SuperAPI information.');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: serviceId,
        actionName: 'resetSuperapi'
      });

      if (response.data?.success) {
        setSuccess('SuperAPI service reset successfully!');
      } else {
        setError('Failed to reset SuperAPI service.');
      }
    } catch (err) {
      console.error('Error resetting SuperAPI:', err);
      setError('Network error occurred while resetting SuperAPI service.');
    } finally {
      setSaving(false);
    }
  };

  const handleRedeem = async () => {
    const delta = prompt('Enter the amount to redeem (in bytes):');
    if (!delta || isNaN(Number(delta))) {
      setError('Please enter a valid number for redeem amount.');
      return;
    }

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: serviceId,
        actionName: 'redeemSuperapi',
        data: { delta: parseInt(delta) }
      });

      if (response.data?.success) {
        setSuccess(`Successfully redeemed ${delta} bytes from SuperAPI service!`);
      } else {
        setError('Failed to redeem from SuperAPI service.');
      }
    } catch (err) {
      console.error('Error redeeming SuperAPI:', err);
      setError('Network error occurred while redeeming from SuperAPI service.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  if (error && !apiInfo) {
    return (
      <Box>
        <Text color="error">{error}</Text>
      </Box>
    );
  }

  const formatBytes = (bytes) => {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const value = bytes / Math.pow(k, i);
    return `${value.toFixed(2)} ${sizes[i]}`;
  };

  const getStatusBadge = (status) => {
    if (status === 1) {
      return <Badge variant="success">Active</Badge>;
    } else {
      return <Badge variant="danger">Suspended</Badge>;
    }
  };

  return (
    <Box p="lg">
      {/* 页面标题 */}
      <Box mb="xl" style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '20px',
        borderRadius: '8px',
        color: 'white',
        textAlign: 'center'
      }}>
        <Text variant="h3" style={{ margin: 0, fontWeight: 700, color: 'white' }}>
          Edit SuperAPI Information
        </Text>
        <Text variant="lg" style={{ margin: '8px 0 0 0', opacity: 0.9, color: 'white' }}>
          Manage your SuperAPI service configuration
        </Text>
      </Box>

      {/* 错误和成功消息 */}
      {error && (
        <MessageBox mb="lg" variant="danger">
          <Box display="flex" alignItems="center" gap="sm">
            <Icon icon="AlertTriangle" />
            {error}
          </Box>
        </MessageBox>
      )}

      {success && (
        <MessageBox mb="lg" variant="success">
          <Box display="flex" alignItems="center" gap="sm">
            <Icon icon="CheckCircle" />
            {success}
          </Box>
        </MessageBox>
      )}

      {/* 当前服务信息 */}
      <InfoCard title="Current Service Information" variant="primary">
        <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(250px, 1fr))" gap="lg">
          <Box
            p="md"
            style={{
              backgroundColor: '#f8f9fa',
              borderRadius: '6px',
              border: '1px solid #e9ecef'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Service ID
            </Text>
            <Text variant="h5" mt="xs" style={{ fontWeight: 700, color: '#495057' }}>
              {apiInfo?.id}
            </Text>
          </Box>

          <Box
            p="md"
            style={{
              backgroundColor: '#f8f9fa',
              borderRadius: '6px',
              border: '1px solid #e9ecef'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Client ID
            </Text>
            <Text variant="h5" mt="xs" style={{ fontWeight: 700, color: '#495057' }}>
              {apiInfo?.clientId}
            </Text>
          </Box>

          <Box
            p="md"
            style={{
              backgroundColor: '#f8f9fa',
              borderRadius: '6px',
              border: '1px solid #e9ecef'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Client
            </Text>
            <Text variant="h5" mt="xs" style={{ fontWeight: 700, color: '#495057' }}>
              {apiInfo?.client}
            </Text>
          </Box>

          <Box
            p="md"
            style={{
              backgroundColor: '#f8f9fa',
              borderRadius: '6px',
              border: '1px solid #e9ecef'
            }}
          >
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Current Status
            </Text>
            <Box mt="xs">
              {getStatusBadge(apiInfo?.status)}
            </Box>
          </Box>
        </Box>

        <Box mt="lg" display="grid" gridTemplateColumns="1fr 1fr 1fr" gap="lg">
          <Box style={{ textAlign: 'center' }}>
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Upload Usage
            </Text>
            <Text variant="h4" mt="xs" color="primary" style={{ fontWeight: 700 }}>
              {formatBytes(apiInfo?.upload)}
            </Text>
          </Box>

          <Box style={{ textAlign: 'center' }}>
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Download Usage
            </Text>
            <Text variant="h4" mt="xs" color="primary" style={{ fontWeight: 700 }}>
              {formatBytes(apiInfo?.download)}
            </Text>
          </Box>

          <Box style={{ textAlign: 'center' }}>
            <Text variant="xs" color="grey60" style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}>
              Reset Date
            </Text>
            <Text variant="lg" mt="xs" style={{ fontWeight: 600 }}>
              {apiInfo?.resetDate ? new Date(apiInfo.resetDate).toLocaleDateString() : 'Not set'}
            </Text>
          </Box>
        </Box>
      </InfoCard>

      {/* 编辑表单 */}
      <InfoCard title="Edit Configuration">
        <Box display="grid" gridTemplateColumns="1fr 1fr" gap="xl">
          <Box>
            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057', marginBottom: '8px' }}>
                Username
              </Label>
              <Input
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                disabled={saving}
                style={{ fontFamily: 'monospace' }}
              />
            </FormGroup>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057', marginBottom: '8px' }}>
                Password
              </Label>
              <Input
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                disabled={saving}
                style={{ fontFamily: 'monospace' }}
              />
            </FormGroup>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057', marginBottom: '8px' }}>
                Status
              </Label>
              <Box>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', parseInt(e.target.value))}
                  disabled={saving}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #ced4da',
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: saving ? '#f8f9fa' : 'white'
                  }}
                >
                  <option value={0}>Suspended</option>
                  <option value={1}>Active</option>
                </select>
              </Box>
            </FormGroup>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057', marginBottom: '8px' }}>
                Reset Type
              </Label>
              <Box>
                <select
                  value={formData.resetType}
                  onChange={(e) => handleInputChange('resetType', parseInt(e.target.value))}
                  disabled={saving}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '1px solid #ced4da',
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: saving ? '#f8f9fa' : 'white'
                  }}
                >
                  <option value={0}>Never</option>
                  <option value={1}>Monthly</option>
                </select>
              </Box>
            </FormGroup>
          </Box>

          <Box>
            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057', marginBottom: '8px' }}>
                Quota Configuration
              </Label>
              <Input
                value={formData.quota}
                onChange={(e) => handleInputChange('quota', e.target.value)}
                disabled={saving}
                placeholder="e.g., [{'traffic': 107374182400, 'speed': 2621440}]"
                style={{ fontFamily: 'monospace', fontSize: '12px' }}
              />
              <Text variant="xs" color="grey60" mt="xs">
                JSON format for traffic and speed limits
              </Text>
            </FormGroup>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057', marginBottom: '8px' }}>
                Expired At
              </Label>
              <Input
                type="date"
                value={formData.expiredAt}
                onChange={(e) => handleInputChange('expiredAt', e.target.value)}
                disabled={saving}
              />
            </FormGroup>

            <FormGroup>
              <Label style={{ fontWeight: 600, color: '#495057', marginBottom: '8px' }}>
                Speed Limit (bytes/s)
              </Label>
              <Input
                type="number"
                value={formData.speedLimit}
                onChange={(e) => handleInputChange('speedLimit', parseInt(e.target.value) || 0)}
                disabled={saving}
                placeholder="0 for unlimited"
              />
              <Text variant="xs" color="grey60" mt="xs">
                Set to 0 for unlimited speed
              </Text>
            </FormGroup>
          </Box>
        </Box>
      </InfoCard>

      {/* 操作按钮 */}
      <InfoCard title="Actions">
        <Box display="grid" gridTemplateColumns="repeat(auto-fit, minmax(200px, 1fr))" gap="lg">
          <Box
            p="lg"
            style={{
              backgroundColor: '#e3f2fd',
              borderRadius: '8px',
              border: '1px solid #bbdefb',
              textAlign: 'center'
            }}
          >
            <Icon icon="Save" style={{ fontSize: '24px', color: '#1976d2', marginBottom: '8px' }} />
            <Text variant="lg" style={{ fontWeight: 600, color: '#1976d2', marginBottom: '8px' }}>
              Save Changes
            </Text>
            <Text variant="sm" color="grey60" style={{ marginBottom: '16px' }}>
              Apply all configuration changes
            </Text>
            <Button
              variant="primary"
              onClick={handleSave}
              disabled={saving}
              style={{ width: '100%' }}
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
          </Box>

          <Box
            p="lg"
            style={{
              backgroundColor: '#fff3e0',
              borderRadius: '8px',
              border: '1px solid #ffcc02',
              textAlign: 'center'
            }}
          >
            <Icon icon="RotateCcw" style={{ fontSize: '24px', color: '#f57c00', marginBottom: '8px' }} />
            <Text variant="lg" style={{ fontWeight: 600, color: '#f57c00', marginBottom: '8px' }}>
              Reset Service
            </Text>
            <Text variant="sm" color="grey60" style={{ marginBottom: '16px' }}>
              Reset usage statistics
            </Text>
            <Button
              variant="secondary"
              onClick={handleReset}
              disabled={saving}
              style={{ width: '100%', backgroundColor: '#ff9800', borderColor: '#ff9800', color: 'white' }}
            >
              Reset Service
            </Button>
          </Box>

          <Box
            p="lg"
            style={{
              backgroundColor: '#f3e5f5',
              borderRadius: '8px',
              border: '1px solid #ce93d8',
              textAlign: 'center'
            }}
          >
            <Icon icon="Download" style={{ fontSize: '24px', color: '#7b1fa2', marginBottom: '8px' }} />
            <Text variant="lg" style={{ fontWeight: 600, color: '#7b1fa2', marginBottom: '8px' }}>
              Redeem Data
            </Text>
            <Text variant="sm" color="grey60" style={{ marginBottom: '16px' }}>
              Reduce usage amount
            </Text>
            <Button
              variant="secondary"
              onClick={handleRedeem}
              disabled={saving}
              style={{ width: '100%', backgroundColor: '#9c27b0', borderColor: '#9c27b0', color: 'white' }}
            >
              Redeem Data
            </Button>
          </Box>
        </Box>

        <Box mt="lg" p="md" style={{
          backgroundColor: '#fff8e1',
          borderRadius: '6px',
          border: '1px solid #ffecb3'
        }}>
          <Box display="flex" alignItems="center" gap="sm">
            <Icon icon="Info" style={{ color: '#ff8f00' }} />
            <Text variant="sm" style={{ fontWeight: 600, color: '#e65100' }}>
              Important Notes:
            </Text>
          </Box>
          <Text variant="sm" color="grey70" mt="xs">
            • Save Changes: Updates the service configuration on the SuperAPI server<br/>
            • Reset Service: Clears usage statistics and resets counters<br/>
            • Redeem Data: Reduces the current usage amount by specified bytes
          </Text>
        </Box>
      </InfoCard>
    </Box>
  );
};

export default EditSuperAPI;
