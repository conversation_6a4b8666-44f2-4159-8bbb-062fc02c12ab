import React from 'react';
import { Box, Text, Button } from '@adminjs/design-system';
import styled from 'styled-components';
import Card from './Card.js';
import CardHeader from './CardHeader.js';
import { Service } from '../types/index.js';

const ServiceItem = styled(Box)<{ isSelected?: boolean }>`
  padding: 16px;
  background: ${(props: any) => props.isSelected ? '#e0f2fe' : '#f3f4f6'};
  border: 1px solid ${(props: any) => props.isSelected ? '#0ea5e9' : '#9ca3af'};
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 12px;

  &:hover {
    background: ${(props: any) => props.isSelected ? '#e0f2fe' : '#e5e7eb'};
  }
`;

interface ServicesListProps {
  userServices: Service[];
  isLoadingServices: boolean;
  selectedUser: any;
  currentService: Service | null;
  onServiceSelect: (service: Service) => void;
}

const ServicesList: React.FC<ServicesListProps> = ({
  userServices,
  isLoadingServices,
  selectedUser,
  currentService,
  onServiceSelect
}) => {
  return (
    <Card>
      <CardHeader
        title="Services List"
        subtitle={userServices.length > 0 ? `Total: ${userServices.length}` : undefined}
      />

      {isLoadingServices ? (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">Loading services...</Text>
        </Box>
      ) : selectedUser ? (
        userServices.length > 0 ? (
          <Box>
            {userServices.map(service => (
              <ServiceItem
                key={service.id}
                isSelected={currentService?.id === service.id}
                onClick={() => onServiceSelect(service)}
              >
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Text fontWeight="bold">#{service.id}</Text>
                    <Text color="grey60">{service.params?.name || 'Unnamed Service'}</Text>
                  </Box>
                  <Button
                    variant={currentService?.id === service.id ? "primary" : "text"}
                    size="sm"
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      onServiceSelect(service);
                    }}
                  >
                    {currentService?.id === service.id ? 'Selected' : 'Select'}
                  </Button>
                </Box>
              </ServiceItem>
            ))}
          </Box>
        ) : (
          <Box textAlign="center" padding="xl">
            <Text color="grey60">No services found</Text>
          </Box>
        )
      ) : (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">Select a user to see their services</Text>
        </Box>
      )}
    </Card>
  );
};

export default ServicesList;
