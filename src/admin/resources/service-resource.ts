import { Service } from "../models/service.entity.js";
import { SuperapiService } from "./superapi-service.js";
import { ResourceWithOptions } from "adminjs";
import { Components } from '../component-loader.js';

const superapiService = new SuperapiService();

export const ServiceResource: ResourceWithOptions = {
    resource: Service,
    options: {
      // 自定义详情页面字段显示顺序
      showProperties: [
        'id',
        'userid',
        'packageid',
        'domainstatus',
        'billingcycle',
        'regdate',
        'nextduedate',
        'firstpaymentamount',
        'amount',
        'username',
        'password',
        'notes',
        // 关联数据 - 放在最后
        'serviceInvoices'
      ],
      properties: {
        id: {
          isVisible: { list: true, filter: true, show: true, edit: false },
          type: 'number',
          isTitle: true
        },
        userid: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'reference',
          reference: 'User'
        },
        packageid: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'reference',
          reference: 'Product'
        },
        billingcycle: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'string'
        },
        regdate: {
          isVisible: { list: true, filter: true, show: true, edit: false },
          type: 'date'
        },
        nextduedate: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'datetime'
        },
        domainstatus: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'string',
          availableValues: [
            { value: 'Pending', label: 'Pending' },
            { value: 'Active', label: 'Active' },
            { value: 'Suspended', label: 'Suspended' },
            { value: 'Terminated', label: 'Terminated' },
            { value: 'Cancelled', label: 'Cancelled' },
            { value: 'Fraud', label: 'Fraud' }
          ]
        },
        firstpaymentamount: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'number'
        },
        amount: {
          isVisible: { list: true, filter: true, show: true, edit: true },
          type: 'number'
        },
        username: {
          isVisible: { list: false, filter: false, show: true, edit: true },
          type: 'string'
        },
        password: {
          isVisible: { list: false, filter: false, show: false, edit: true },
          type: 'password'
        },
        notes: {
          isVisible: { list: false, filter: false, show: true, edit: true },
          type: 'textarea'
        },
        serviceInvoices: {
          isVisible: { list: false, filter: false, show: true, edit: false },
          type: 'string',
          components: {
            show: Components.ServiceInvoices
          }
        }
      },
      actions: {
        edit: {
          after: async (response, request, context) => {
            const updatedRecord = response.record;
            if (request.payload.packageid) {
              await superapiService.syncServicePackage(updatedRecord.params.id, +request.payload.packageid);
            } 
            
            if (request.payload.nextduedate) {
              await superapiService.syncServiceExpireDate(updatedRecord.params.id, request.payload.nextduedate);
            }
            return response;
          },
        },
        superapi: {
          actionType: 'record',
          component: Components.SuperAPI,
          handler: async (request, response, context) => {
            console.log('superapi action called!', {
              request: request.params,
              context: context.record?.params,
              resourceId: context.resource?.id()
            });

            const serviceId =
              context.record?.param?.('id') ||
              context.record?.params?.id ||
              request.params?.recordId ||
              request.query?.serviceId;

            console.log('Extracted serviceId:', serviceId);

            const service = await superapiService.fetchService({ serviceId: serviceId });
            console.log('Fetched service:', service);

            // 返回符合 AdminJS RecordJSON 结构的对象
            // 根据文档，record action 的 handler 必须返回 { record: record.toJSON(currentAdmin) }
            const recordJSON = context.record?.toJSON(context.currentAdmin);
            if (!recordJSON) {
              throw new Error('No record found');
            }

            // 将 SuperAPI 数据添加到 record 的 params 中
            recordJSON.params = {
              ...recordJSON.params,
              superapiData: service
            };

            console.log('Returning record with SuperAPI data:', recordJSON);

            return {
              record: recordJSON
            };
          },
        },
        editSuperapi: {
          actionType: 'record',
          component: Components.EditSuperAPI,
          handler: async (request, response, context) => {
            console.log('editSuperapi action called!', {
              request: request.params,
              context: context.record?.params,
              resourceId: context.resource?.id()
            });

            const serviceId =
              context.record?.param?.('id') ||
              context.record?.params?.id ||
              request.params?.recordId ||
              request.query?.serviceId;

            console.log('Extracted serviceId:', serviceId);

            const service = await superapiService.fetchService({ serviceId: serviceId });
            console.log('Fetched service:', service);

            // 返回符合 AdminJS RecordJSON 结构的对象
            const recordJSON = context.record?.toJSON(context.currentAdmin);
            if (!recordJSON) {
              throw new Error('No record found');
            }

            // 将 SuperAPI 数据添加到 record 的 params 中
            recordJSON.params = {
              ...recordJSON.params,
              superapiData: service
            };

            console.log('Returning record with SuperAPI data:', recordJSON);

            return {
              record: recordJSON
            };
          },
        },
        updateSuperapi: {
          actionType: 'record',
          showInDrawer: false,
          isVisible: false,
          isAccessible: () => true,
          handler: async (request, response, context) => {
            console.log('updateSuperapi action called!', {
              request: request.params,
              payload: request.payload,
              context: context.record?.params,
            });

            const serviceId =
              context.record?.params?.id ||
              request.params?.recordId;

            if (!serviceId) {
              throw new Error('Service ID not found');
            }

            const updateData = request.payload;
            console.log('Update data:', updateData);

            try {
              await superapiService.updateService(serviceId, updateData);

              return {
                record: context.record?.toJSON(context.currentAdmin),
                notice: {
                  message: 'SuperAPI information updated successfully!',
                  type: 'success'
                }
              };
            } catch (error) {
              console.error('Error updating SuperAPI:', error);
              throw new Error('Failed to update SuperAPI information');
            }
          },
        },
        resetSuperapi: {
          actionType: 'record',
          showInDrawer: false,
          isVisible: false,
          isAccessible: () => true,
          handler: async (request, response, context) => {
            console.log('resetSuperapi action called!', {
              request: request.params,
              context: context.record?.params,
            });

            const serviceId =
              context.record?.params?.id ||
              request.params?.recordId;

            if (!serviceId) {
              throw new Error('Service ID not found');
            }

            try {
              await superapiService.reset(serviceId);

              return {
                record: context.record?.toJSON(context.currentAdmin),
                notice: {
                  message: 'SuperAPI service reset successfully!',
                  type: 'success'
                }
              };
            } catch (error) {
              console.error('Error resetting SuperAPI:', error);
              throw new Error('Failed to reset SuperAPI service');
            }
          },
        },
        redeemSuperapi: {
          actionType: 'record',
          showInDrawer: false,
          isVisible: false,
          isAccessible: () => true,
          handler: async (request, response, context) => {
            console.log('redeemSuperapi action called!', {
              request: request.params,
              payload: request.payload,
              context: context.record?.params,
            });

            const serviceId =
              context.record?.params?.id ||
              request.params?.recordId;

            const delta = request.payload?.delta;

            if (!serviceId) {
              throw new Error('Service ID not found');
            }

            if (!delta || isNaN(delta)) {
              throw new Error('Invalid redeem amount');
            }

            try {
              await superapiService.redeem(serviceId, delta);

              return {
                record: context.record?.toJSON(context.currentAdmin),
                notice: {
                  message: `Successfully redeemed ${delta} bytes from SuperAPI service!`,
                  type: 'success'
                }
              };
            } catch (error) {
              console.error('Error redeeming SuperAPI:', error);
              throw new Error('Failed to redeem from SuperAPI service');
            }
          },
        },
      },
    },
  };